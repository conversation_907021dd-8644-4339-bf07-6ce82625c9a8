"use client";

import { Button1 } from "@/components/Button1";
import { Bad<PERSON>, Button } from "@relume_io/relume-ui";
import { motion } from "framer-motion";
import React from "react";
import { RxChevronRight } from "react-icons/rx";

export function Blog40() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <motion.div
          className="mb-12 md:mb-18 lg:mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="w-full max-w-lg">
            <h2 className="rb-5 mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
              Cases
            </h2>
          </div>
        </motion.div>
        <motion.div
          className="grid grid-cols-1 gap-x-8 gap-y-12 md:grid-cols-2 md:gap-y-16 lg:grid-cols-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.15
              }
            }
          }}
        >
          <motion.div
            className="flex size-full flex-col items-center justify-start rounded-xl border border-border"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <a href="#" className="w-full">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl hover:text-link-primary">
                  The Future of Sustainable Events
                </h2>
              </a>
              <p>
                Learn how sustainable practices are transforming the events
                industry.
              </p>
              <Button1
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6"
              >
                Read more
              </Button1>
            </div>
          </motion.div>
          <motion.div
            className="flex size-full flex-col items-center justify-start rounded-xl border border-border"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <a href="#" className="w-full">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl hover:text-link-primary">
                  The Future of Sustainable Events
                </h2>
              </a>
              <p>
                Learn how sustainable practices are transforming the events
                industry.
              </p>
              <Button1
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6"
              >
                Read more
              </Button1>
            </div>
          </motion.div>
          <motion.div
            className="flex size-full flex-col items-center justify-start rounded-xl border border-border"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
          >
            <a href="#" className="w-full">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image-landscape.svg"
                alt="Relume placeholder image"
                className="aspect-[3/2] size-full object-cover rounded-t-lg"
              />
            </a>
            <div className="px-5 py-6 md:p-6">
              <div className="rb-4 mb-4 flex w-full items-center justify-start">
                <Badge className="mr-4 bg-transparent border border-border rounded-md text-text-primary py-1 px-3">Insights</Badge>
                <p className="inline text-sm font-semibold">5 min read</p>
              </div>
              <a className="mb-2 block max-w-full" href="#">
                <h2 className="text-xl font-semibold md:text-2xl hover:text-link-primary">
                  The Future of Sustainable Events
                </h2>
              </a>
              <p>
                Learn how sustainable practices are transforming the events
                industry.
              </p>
              <Button1
                title="Read more"
                variant="link"
                size="link"
                iconRight={<RxChevronRight />}
                className="mt-6"
              >
                Read more
              </Button1>
            </div>
          </motion.div>
        </motion.div>
        <motion.div
          className="flex items-center justify-end"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Button1 variant="link" className="mt-10 md:mt-14 lg:mt-16">
            Heb je een project? Let's talk!
          </Button1>
        </motion.div>
      </div>
    </section>
  );
}

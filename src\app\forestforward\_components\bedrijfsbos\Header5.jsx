"use client";

import { Button1 } from "@/components/Button1";
import { But<PERSON> } from "@relume_io/relume-ui";
import React from "react";

export function Header5() {
  return (
    <section id="relume" className="relative px-[5%]">
      <div className="relative z-10 container">
        <div className="flex max-h-[60rem] min-h-svh items-center py-16 md:py-24 lg:py-28">
          <div className="max-w-md">
            <h1 className="mb-5 text-6xl font-semibold text-text-alternative md:mb-6 md:text-9xl lg:text-10xl">
              "De beste manier om mensen achter je duurzaam verhaal te scharen"
            </h1>
            <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
              <Button1 title="Let's talk!" href="/contact" variant="filled">Let's talk!</Button1>
              <Button1 title="Meer lezen" variant="transparent">
                Meer lezen
              </Button1>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 z-0">
        <img
          src="/images/forestforward/bedrijfsbos/10.png"
          className="size-full object-cover"
          alt="Relume placeholder image"
        />
        <div className="absolute inset-0 bg-black/50" />
      </div>
    </section>
  );
}

"use client";

import { Button1 } from "@/components/Button1";
import React, { Fragment } from "react";


export function Content17() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="grid grid-cols-1 gap-5 md:grid-cols-[1fr_1.5fr] md:gap-x-12 lg:gap-x-20">
          <div>
            <h3 className="text-4xl leading-[1.2] font-semibold md:text-5xl lg:text-6xl">
              Bedrijfsbos
            </h3>
          </div>
          <div>
            <div>
              <Fragment>
                <p>
                  Een bedrij<PERSON> is méér dan een verzameling bomen. Het is een blijvend symbool van maatschappelijke en ecologische betrokkenheid. Iets waardoor je duurzaamheid uit de vergaderzaal haalt en het voor je mensen zéér concreet maakt. Door een publiek bos dicht bij jouw bedrijf aan te leggen, creëer je een groene plek voor je medewerkers en toon je je engagement aan de buurt. En niets zo leuk als samen de mouwen oprollen en tezamen aanplanten. Dat maakt mensen trots, en betrokken.
                </p>
                
              </Fragment>
              <Button1
              title="Contacteer ons" 
              variant="transparent-light" 
              href="/contact"
              className="mt-6">
                Contacteer ons
              </Button1>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
